/**
 * OpenAI 流式输出使用示例
 * 展示如何使用流式API和JSON修复功能
 */

import { 
  initializeOpenAI, 
  generateTextStreamWithLineProcessing,
  tryParseAndRepairJSON,
  getOpenAIService 
} from '../lib/openaiService';
import { loadOpenAIConfig } from '../lib/openaiConfig';
import { processKeywordsForWordCloud } from '../lib/wordcloudUtils';

/**
 * 示例1: 基本流式文本生成
 */
export async function basicStreamingExample() {
  try {
    // 加载配置并初始化
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('请先设置 OpenAI API Key');
      return;
    }

    initializeOpenAI(config.apiKey, config.baseURL);

    console.log('开始流式生成文本...');
    
    const fullResponse = await generateTextStreamWithLineProcessing(
      '请写一首关于春天的短诗',
      (line: string, accumulatedText: string) => {
        console.log('收到新行:', line);
        console.log('累积文本长度:', accumulatedText.length);
      },
      config.model
    );

    console.log('完整响应:', fullResponse);
  } catch (error) {
    console.error('流式生成失败:', error);
  }
}

/**
 * 示例2: 流式JSON生成和修复
 */
export async function streamingJSONExample() {
  try {
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('请先设置 OpenAI API Key');
      return;
    }

    initializeOpenAI(config.apiKey, config.baseURL);

    const prompt = `请分析以下文章标题，提取关键词并返回JSON格式：
[
  {"id": 1, "title": "人工智能在医疗领域的应用前景"},
  {"id": 2, "title": "机器学习算法优化技术研究"},
  {"id": 3, "title": "深度学习在图像识别中的突破"}
]

请返回格式：
[
  {
    "keyword": "关键词",
    "aliases": ["同义词1", "同义词2"],
    "ids": [1, 2, 3]
  }
]`;

    console.log('开始流式生成JSON...');
    
    let lastValidData: any[] = [];
    
    const fullResponse = await generateTextStreamWithLineProcessing(
      prompt,
      (line: string, accumulatedText: string) => {
        console.log('收到新行:', line);
        
        // 尝试解析当前累积的JSON
        const parsedData = tryParseAndRepairJSON(accumulatedText);
        if (parsedData && Array.isArray(parsedData)) {
          // 检查是否有新数据
          if (JSON.stringify(parsedData) !== JSON.stringify(lastValidData)) {
            lastValidData = parsedData;
            console.log('解析到新的关键词数据:', parsedData);
            
            // 实时更新词云（示例）
            processKeywordsForWordCloud(parsedData).then(wordCloudData => {
              console.log('实时词云数据:', wordCloudData);
            }).catch(error => {
              console.warn('词云处理失败:', error);
            });
          }
        }
      },
      config.model
    );

    console.log('完整JSON响应:', fullResponse);
    
    // 最终解析
    const finalData = tryParseAndRepairJSON(fullResponse);
    console.log('最终解析结果:', finalData);
    
  } catch (error) {
    console.error('流式JSON生成失败:', error);
  }
}

/**
 * 示例3: JSON修复功能测试
 */
export async function jsonRepairExample() {
  console.log('测试JSON修复功能...');
  
  // 测试各种不完整的JSON
  const testCases = [
    '{"keyword": "人工智能", "ids": [1, 2',  // 不完整的数组
    '[{"keyword": "机器学习"}, {"keyword"',   // 不完整的对象
    '{"keyword": "深度学习", "ids": [1, 2, 3]}]',  // 多余的括号
    '[{"keyword": "AI", "ids": [1, 2, 3]',  // 缺少结束括号
  ];
  
  for (const testCase of testCases) {
    console.log('\n测试用例:', testCase);
    
    try {
      const repaired = tryParseAndRepairJSON(testCase);
      console.log('修复结果:', repaired);
    } catch (error) {
      console.log('修复失败:', error.message);
    }
  }
}

/**
 * 示例4: 模拟词云实时更新
 */
export async function simulateWordCloudUpdate() {
  try {
    const config = await loadOpenAIConfig();
    if (!config.apiKey) {
      console.error('请先设置 OpenAI API Key');
      return;
    }

    initializeOpenAI(config.apiKey, config.baseURL);

    // 模拟文章数据
    const mockPosts = [
      { id: 1, title: "Vue.js 3.0 新特性详解" },
      { id: 2, title: "React Hooks 最佳实践" },
      { id: 3, title: "TypeScript 高级类型应用" },
      { id: 4, title: "前端性能优化技巧" },
      { id: 5, title: "JavaScript ES2023 新功能" }
    ];

    const prompt = `请分析以下文章标题，提取关键词：\n${JSON.stringify(mockPosts, null, 2)}\n\n返回JSON格式的关键词数据。`;

    console.log('开始模拟词云实时更新...');
    
    let updateCount = 0;
    
    await generateTextStreamWithLineProcessing(
      prompt,
      async (line: string, accumulatedText: string) => {
        updateCount++;
        console.log(`\n=== 更新 #${updateCount} ===`);
        console.log('新行:', line);
        
        // 尝试解析并更新词云
        const parsedData = tryParseAndRepairJSON(accumulatedText);
        if (parsedData && Array.isArray(parsedData) && parsedData.length > 0) {
          try {
            const wordCloudData = await processKeywordsForWordCloud(parsedData);
            console.log('词云更新:', wordCloudData.map(item => `${item.text}(${item.size})`).join(', '));
          } catch (error) {
            console.log('词云处理中...', error.message);
          }
        } else {
          console.log('等待更多数据...');
        }
      },
      config.model
    );

    console.log('\n词云更新完成！');
  } catch (error) {
    console.error('模拟更新失败:', error);
  }
}

// 导出所有示例函数
export const streamingExamples = {
  basicStreamingExample,
  streamingJSONExample,
  jsonRepairExample,
  simulateWordCloudUpdate
};

// 运行所有示例的便捷函数
export async function runAllStreamingExamples() {
  console.log('=== OpenAI 流式输出示例 ===\n');
  
  console.log('1. 基本流式文本生成');
  await basicStreamingExample();
  
  console.log('\n2. 流式JSON生成和修复');
  await streamingJSONExample();
  
  console.log('\n3. JSON修复功能测试');
  await jsonRepairExample();
  
  console.log('\n4. 模拟词云实时更新');
  await simulateWordCloudUpdate();
  
  console.log('\n=== 所有示例运行完成 ===');
}