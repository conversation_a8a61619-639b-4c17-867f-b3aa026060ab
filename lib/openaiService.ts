import OpenAI from 'openai';
import { jsonrepair } from 'jsonrepair';

// OpenAI 服务类
export class OpenAIService {
  private client: OpenAI;

  constructor(apiKey: string, baseURL?: string) {
    this.client = new OpenAI({
      apiKey,
      baseURL,
      dangerouslyAllowBrowser: true // 允许在浏览器环境中使用
    });
  }

  /**
   * 生成聊天回复
   * @param messages 消息历史
   * @param model 模型名称，默认为 gpt-3.5-turbo
   * @param maxTokens 最大token数
   * @returns 生成的回复
   */
  async generateChatCompletion(
    messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    model: string = 'gpt-3.5-turbo',
    maxTokens: number = 1000
  ): Promise<string> {
    try {
      const completion = await this.client.chat.completions.create({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
      });

      return completion.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenAI API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 生成文本内容
   * @param prompt 提示词
   * @param model 模型名称
   * @param maxTokens 最大token数
   * @returns 生成的文本
   */
  async generateText(
    prompt: string,
    model: string = 'gpt-3.5-turbo',
    maxTokens: number = 1000
  ): Promise<string> {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'user', content: prompt }
    ];

    return this.generateChatCompletion(messages, model, maxTokens);
  }

  /**
   * 流式生成聊天回复
   * @param messages 消息历史
   * @param model 模型名称
   * @param onChunk 接收流式数据的回调函数
   * @param maxTokens 最大token数
   */
  async generateChatCompletionStream(
    messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
    model: string = 'gpt-3.5-turbo',
    onChunk: (chunk: string) => void,
    maxTokens: number = 1000
  ): Promise<void> {
    try {
      const stream = await this.client.chat.completions.create({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          onChunk(content);
        }
      }
    } catch (error) {
      console.error('OpenAI 流式API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 流式生成文本内容，支持JSON修复和按行处理
   * @param prompt 提示词
   * @param model 模型名称
   * @param onLineComplete 接收完整行数据的回调函数
   * @param maxTokens 最大token数
   * @returns 完整的生成文本
   */
  async generateTextStreamWithLineProcessing(
    prompt: string,
    model: string = 'gpt-3.5-turbo',
    onLineComplete: (line: string, accumulatedText: string) => void,
    maxTokens: number = 1000
  ): Promise<string> {
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'user', content: prompt }
    ];

    let accumulatedText = '';
    let currentLine = '';

    try {
      const stream = await this.client.chat.completions.create({
        model,
        messages,
        max_tokens: maxTokens,
        temperature: 0.7,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) {
          accumulatedText += content;
          currentLine += content;

          // 检查是否包含换行符
          const lines = currentLine.split('\n');
          if (lines.length > 1) {
            // 处理完整的行（除了最后一个不完整的行）
            for (let i = 0; i < lines.length - 1; i++) {
              const completeLine = lines[i];
              if (completeLine.trim()) {
                onLineComplete(completeLine, accumulatedText);
              }
            }
            // 保留最后一个不完整的行
            currentLine = lines[lines.length - 1];
          }
        }
      }

      // 处理最后一行（如果有的话）
      if (currentLine.trim()) {
        onLineComplete(currentLine, accumulatedText);
      }

      return accumulatedText;
    } catch (error) {
      console.error('OpenAI 流式API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 尝试修复并解析JSON数据
   * @param jsonText 可能不完整的JSON文本
   * @returns 解析后的对象，如果失败返回null
   */
  tryParseAndRepairJSON(jsonText: string): any {
    // 如果输入为空或只是标记符，直接返回null
    if (!jsonText || jsonText.trim() === '' || jsonText.trim() === '```json' || jsonText.trim() === '```') {
      return null;
    }

    try {
      // 首先尝试直接解析
      return JSON.parse(jsonText);
    } catch (error) {
      try {
        // 清理可能的markdown标记
        let cleanedJson = jsonText.replace(/```json\s*/g, '').replace(/```\s*$/g, '').trim();
        
        // 如果清理后为空，返回null
        if (!cleanedJson) {
          return null;
        }

        // 如果直接解析失败，尝试使用jsonrepair修复
        const repairedJson = jsonrepair(cleanedJson);
        return JSON.parse(repairedJson);
      } catch (repairError) {
        // 如果修复也失败，返回null
        console.debug('JSON修复失败:', repairError.message, '原始文本:', jsonText);
        return null;
      }
    }
  }
}

// 创建默认的 OpenAI 服务实例
let defaultOpenAIService: OpenAIService | null = null;

/**
 * 初始化默认的 OpenAI 服务
 * @param apiKey API密钥
 * @param baseURL 可选的基础URL
 */
export function initializeOpenAI(apiKey: string, baseURL?: string): void {
  defaultOpenAIService = new OpenAIService(apiKey, baseURL);
}

/**
 * 获取默认的 OpenAI 服务实例
 * @returns OpenAI 服务实例
 */
export function getOpenAIService(): OpenAIService {
  if (!defaultOpenAIService) {
    throw new Error('OpenAI 服务未初始化，请先调用 initializeOpenAI()');
  }
  return defaultOpenAIService;
}

// 便捷函数
export async function generateText(prompt: string, model?: string): Promise<string> {
  return getOpenAIService().generateText(prompt, model);
}

export async function generateChatCompletion(
  messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[],
  model?: string
): Promise<string> {
  return getOpenAIService().generateChatCompletion(messages, model);
}

export async function generateTextStreamWithLineProcessing(
  prompt: string,
  onLineComplete: (line: string, accumulatedText: string) => void,
  model?: string
): Promise<string> {
  return getOpenAIService().generateTextStreamWithLineProcessing(prompt, model, onLineComplete);
}

export function tryParseAndRepairJSON(jsonText: string): any {
  return getOpenAIService().tryParseAndRepairJSON(jsonText);
}
